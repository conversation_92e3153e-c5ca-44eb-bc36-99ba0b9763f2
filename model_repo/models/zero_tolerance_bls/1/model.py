import triton_python_backend_utils as pb_utils

import json
import numpy as np
import os
from .matcher import init_zt_lists, zt_search_batch

# Debug setup - only if ENABLE_DEBUG is true
if os.getenv('ENABLE_DEBUG', 'false').lower() == 'true':
    try:
        import debugpy
        debug_port = int(os.getenv('DEBUG_PORT', '5678'))
        print(f"Debug mode enabled - connecting to debugger on host.docker.internal:{debug_port}")
        debugpy.connect(('host.docker.internal', debug_port))
        print("Connected to debugger!")
    except Exception as e:
        print(f"Failed to connect to debugger: {e}")
        print("Continuing without debug...")

class TritonPythonModel:
    """This model loops through different dtypes to make sure that
    serialize_byte_tensor works correctly in the Python backend.
    """
    def initialize(self, args):
        parameters = json.loads(args['model_config'])['parameters']
        for key, value in parameters.items():
            parameters[key] = value["string_value"]

        # Initialize lists from all files in the specified directory
        zt_file_path = parameters["zt_model_location"]
        self.zt_lists = init_zt_lists(zt_file_path)
        self.str_dtype = pb_utils.triton_string_to_numpy("TYPE_STRING")

    def execute(self, requests):
        """
        Processes a batch of inference requests by decoding input text, performing 
        zero-tolerance searches, and returning the results.

        Args:
            requests (list): A list of Triton Inference Server requests. Each request 
                contains a "TEXT" input tensor with text data.

        Returns:
            list: A list of Triton Inference Server responses. Each response contains 
            a tensor named "ZT_result" with the results of the zero-tolerance search.

        Notes:
            - Input tensors are decoded from byte format to strings.
            - The function performs batch processing using `self.zt_search_batch`.
            - The output tensor has the same batch size as the input and uses the 
            data type specified by `self.output0_dtype`.

        Example:
            Input:
                requests = [
                    Triton Inference Request with "TEXT" tensor of shape (batch_size, 1)
                ]
            Output:
                [
                    Triton Inference Response with "ZT_result" tensor of shape (batch_size,)
                ]
        """
        responses = []
        for request in requests:
            if request.is_cancelled():
               responses.append(pb_utils.InferenceResponse(
                   error=pb_utils.TritonError("Message", pb_utils.TritonError.CANCELLED)))
               continue

            input_text = pb_utils.get_input_tensor_by_name(request, "TEXT").as_numpy()
            tok_batch = []

            for i in range(input_text.shape[0]):
                decoded_object = input_text[i, 0].decode()
                tok_batch.append(decoded_object)

            search_results = zt_search_batch(tok_batch, self.zt_lists)
            responses.append(pb_utils.InferenceResponse([
                pb_utils.Tensor("zt_match", np.array(search_results, dtype=self.str_dtype)),
            ]))

        return responses