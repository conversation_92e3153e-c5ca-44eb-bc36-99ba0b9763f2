#!/usr/bin/env python3
"""
Debug server script for testing the matcher functionality.
This starts a debug server that IntelliJ can connect to for remote debugging.
"""

import debugpy
import sys
import os
from pathlib import Path

# Start debug server
print("Starting debug server on localhost:5678")
debugpy.listen(("localhost", 5678))
print("Waiting for debugger to attach...")
debugpy.wait_for_client()
print("Debugger attached!")

# Add the model directory to Python path so we can import the modules
sys.path.insert(0, str(Path(__file__).parent / "model_repo" / "models" / "zero_tolerance_bls" / "1"))

# Import the matcher module
import matcher

def debug_text_cleaning():
    """Debug the text cleaning functionality"""
    test_texts = [
        "Hello, World!",
        "\tHello,  World!\n",
        "1@3-456_789#0",
        "hello! how mặt are awesome lồn, my friend?",
        "I like the color blue, you degenerate"
    ]
    
    print("=== Text Cleaning Debug ===")
    for text in test_texts:
        cleaned = matcher.clean_text(text)  # Set breakpoint here
        print(f"Original: {repr(text)}")
        print(f"Cleaned:  {repr(cleaned)}")
        print()

def debug_term_loading():
    """Debug loading terms from CSV files"""
    print("=== Term Loading Debug ===")
    
    # Use test data
    test_data_dir = Path("test/unit/data")
    if test_data_dir.exists():
        print(f"Loading terms from: {test_data_dir}")
        zt_lists = matcher.init_zt_lists(str(test_data_dir))  # Set breakpoint here
        
        for list_name, regex_obj in zt_lists.items():
            print(f"List: {list_name}")
            print(f"Regex: {regex_obj.pattern}")
            print()
    else:
        print("Test data directory not found")

def debug_term_matching():
    """Debug term matching functionality"""
    print("=== Term Matching Debug ===")
    
    # Create some test terms
    test_terms = [
        ("badword", "Full string"),
        ("substring", "Substring"),
        ("degenerate", "Full string")
    ]
    
    # Generate regex
    regex_pattern = matcher.get_re_terms_from_term_match_tuple(test_terms)  # Set breakpoint here
    print(f"Generated regex: {regex_pattern}")
    
    # Test strings
    test_strings = [
        "This is a clean message",
        "This contains a badword in it",
        "This has substring matching",
        "You are a degenerate person",
        "This has badwordextended which shouldn't match"
    ]
    
    import re
    compiled_regex = re.compile(regex_pattern, re.IGNORECASE)
    zt_lists = {"test": compiled_regex}
    
    for test_string in test_strings:
        result = matcher.zt_search(test_string, zt_lists)  # Set breakpoint here
        print(f"Text: {repr(test_string)}")
        print(f"Match: {result}")
        print()

def debug_batch_processing():
    """Debug batch processing functionality"""
    print("=== Batch Processing Debug ===")
    
    # Use actual data if available
    data_dir = Path("model_repo/engine/data/1.5.0")
    if data_dir.exists():
        print(f"Loading real ZT lists from: {data_dir}")
        zt_lists = matcher.init_zt_lists(str(data_dir))
        print(f"Loaded {len(zt_lists)} ZT lists: {list(zt_lists.keys())}")
    else:
        print("Real data not available, using test data")
        test_data_dir = Path("test/unit/data")
        if test_data_dir.exists():
            zt_lists = matcher.init_zt_lists(str(test_data_dir))
        else:
            print("No data available for testing")
            return
    
    # Test batch processing
    test_batch = [
        "Hello, this is a clean message",
        "I like the color blue, you degenerate",
        "This might contain some bad terms"
    ]
    
    results = matcher.zt_search_batch(test_batch, zt_lists)  # Set breakpoint here
    
    for i, (text, result) in enumerate(zip(test_batch, results)):
        print(f"Text {i+1}: {repr(text)}")
        print(f"Result: {result}")
        print()

if __name__ == "__main__":
    print("Disruptive Text Matcher Debug Server")
    print("=" * 50)
    
    # Set breakpoints in any of these functions to debug specific functionality
    debug_text_cleaning()
    debug_term_loading()
    debug_term_matching()
    debug_batch_processing()
    
    print("Debug complete!")
