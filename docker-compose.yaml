services:
  # Run using the locally built model repo
  triton:
    image: "registry.rcluster.io/ml-eng/riot-triton-cpu:${RIOT_TRITON_VERSION}"
    container_name: disruptive-text-triton
    ports:
      - "8000-8002:8000-8002"
      - "5678:5678"  # Debug port
    networks:
      - triton-cluster
    environment:
      MODEL_REPO_TYPE: volume
      MODEL_REPO_ROOT: /model_repo
      ENABLE_DEBUG: ${ENABLE_DEBUG:-false}
      DEBUG_PORT: "5678"
    volumes:
      - ./${MODEL_BUILD_DIR}:/model_repo

  # Run using a model repo located in S3
  triton-s3:
    image: "registry.rcluster.io/ml-eng/riot-triton-cpu:${RIOT_TRITON_VERSION}"
    container_name: disruptive-text-triton-s3
    ports:
      - "8000-8002:8000-8002"
    networks:
      - triton-cluster
    environment:
      MODEL_REPO_TYPE: s3
      MODEL_REPO_ROOT: /model_repo
      S3_MODEL_REPO: ${S3_MODEL_REPO}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID:-}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY:-}
      AWS_SESSION_TOKEN: ${AWS_SESSION_TOKEN:-}
      AWS_DEFAULT_REGION: ${AWS_DEFAULT_REGION:-us-west-2}

  triton-tools:
    image: "registry.rcluster.io/ml-eng/riot-triton-tools:${RIOT_TRITON_TOOLS_VERSION}"
    container_name: disruptive-text-triton-tools

    #Triton must start with access to the GPU or startup will fail
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

    ports:
      - "8003:8003"
    networks:
      - triton-cluster
    volumes:
      - ./${MODEL_BUILD_DIR}:/model_repo
  
  conda-disruptive-text:
    build:
      context: ./conda
      args:
        project: disruptive-text
      dockerfile: Dockerfile.conda
    image: "disruptivetext/conda-model-disruptive-text:latest"
    container_name: disruptive-text-conda-model
    volumes:
      - ./:/conda 
      - disruptive_text_cache_volume:/build_cache

    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]

secrets:
  AWS_ACCESS_KEY_ID:
    name: AWS_ACCESS_KEY_ID
    environment: AWS_ACCESS_KEY_ID
  AWS_SECRET_ACCESS_KEY:
    name: AWS_SECRET_ACCESS_KEY
    environment: AWS_SECRET_ACCESS_KEY
  AWS_SESSION_TOKEN:
    name: AWS_SESSION_TOKEN
    environment: AWS_SESSION_TOKEN
  AWS_DEFAULT_REGION:
    name: AWS_DEFAULT_REGION
    environment: AWS_DEFAULT_REGION

networks:
  triton-cluster:

#volumes default to 1GB
volumes:
  #caches download dependencies for pip, apt etc. 
  disruptive_text_cache_volume: