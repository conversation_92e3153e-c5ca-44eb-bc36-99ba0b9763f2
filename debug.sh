#!/bin/bash
# Simple script to start debugging

echo "🐛 Starting Disruptive Text Model in Debug Mode"
echo "================================================"
echo ""
echo "1. Starting container with debug support..."

# Stop any existing containers
make down

# Start with debug enabled
make run-debug &

echo ""
echo "2. Waiting for container to be ready..."
sleep 15

echo ""
echo "3. Container should be ready! Next steps:"
echo "   ✅ Start IntelliJ debug server (click debug button)"
echo "   ✅ Wait for 'Waiting for process connection...'"
echo "   ✅ Make a test request:"
echo ""
echo "   curl -X POST localhost:8000/v2/models/zero_tolerance_bls/infer \\"
echo "     -H \"Content-Type: application/json\" \\"
echo "     -d '{"
echo "       \"inputs\": ["
echo "         {"
echo "           \"name\": \"TEXT\","
echo "           \"shape\": [1, 1],"
echo "           \"datatype\": \"BYTES\","
echo "           \"data\": [\"test debug message\"]"
echo "         }"
echo "       ]"
echo "     }'"
echo ""
echo "   ✅ Your breakpoints should hit!"
echo ""
echo "🎯 Happy debugging!"
