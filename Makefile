# Compose a CPU based Disruptive Text 
#
# For Docker release, please use the Github Actions attached to this project.
include .env
SHELL := /bin/bash
export DOCKER_BUILDKIT=1
export CONDA_S3_PATH
export DOWNLOAD_CONDA

BUILD_SCRIPT_VERSION ?= v2

#downloads conda, TRT, and onnx model into local repo
fetch:
	aws s3 cp $(CONDA_S3_PATH) model_repo/conda/

clean:
	rm -rf conda-env.tar.gz
	rm -rf model_repo/conda/conda-env.tar.gz
	rm -rf $(MODEL_BUILD_DIR)
	rm -rf test/unit/.venv

fetch-build-script:
	mkdir -p tmp
	@tmpdir=$$(mktemp -d) && \
	git clone --quiet --branch $(BUILD_SCRIPT_VERSION) --single-branch \
		********************:riot-actions/shared-playerplat-actions.git $$tmpdir && \
	cp $$tmpdir/ml-eng-build-model-repo/build.sh tmp/build.sh && \
	rm -rf $$tmpdir

# conda image with dependencies needed by this model
conda: build-conda run-conda copy-conda-deps

build-conda:
	COMPOSE_BAKE=true docker compose build conda-disruptive-text

run-conda:
	docker compose up conda-disruptive-text

publish-conda:
	aws s3 cp model_repo/conda/conda-env.tar.gz $(CONDA_S3_PATH)

copy-conda-deps:
	mv disruptive-text-conda-env.tar.gz model_repo/conda/conda-env.tar.gz

# Build a container with the model and dependencies.
build: fetch-build-script
	./tmp/build.sh "$(MODEL_BUILD_DIR)" "$(INFERENCE_VERSION)"

run:
	@if [ "$(ENABLE_DEBUG)" = "true" ]; then \
		echo "Debug mode detected - building debug image..."; \
		$(MAKE) build-debug; \
		TRITON_IMAGE=disruptive-text-triton-debug ENABLE_DEBUG=true docker compose up triton; \
	else \
		docker compose up triton; \
	fi

run-s3:
	docker compose up triton-s3

# Debug targets
build-debug:
	docker build -f Dockerfile.debug -t disruptive-text-triton-debug .

run-debug: build-debug
	TRITON_IMAGE=disruptive-text-triton-debug ENABLE_DEBUG=true docker compose up triton

# stop running container and remove old volume entries
down:
	docker compose down --volumes --remove-orphans

unittest-init:
	test/unit/init.sh

unittest:
	test/unit/run.sh

scenariotest-init:
	test/scenario/init.sh

scenariotest:
	RFC190_LOCATION=${RFC190_LOCATION} test/scenario/run.sh

.PHONY: build unittest-init unittest build-debug run-debug