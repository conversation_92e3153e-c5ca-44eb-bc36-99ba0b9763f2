#!/bin/bash
# <PERSON><PERSON>t to install debugpy in the conda environment used by Triton

echo "Installing debugpy for debugging..."

# Wait for conda environment to be extracted
sleep 5

# Find the conda environment path
CONDA_ENV_PATH="/tmp/conda-env"
if [ -d "$CONDA_ENV_PATH" ]; then
    echo "Found conda environment at $CONDA_ENV_PATH"
    
    # Install debugpy in the conda environment
    if [ -f "$CONDA_ENV_PATH/bin/pip" ]; then
        echo "Installing debugpy with conda pip..."
        $CONDA_ENV_PATH/bin/pip install debugpy
    elif [ -f "$CONDA_ENV_PATH/bin/python" ]; then
        echo "Installing debugpy with conda python..."
        $CONDA_ENV_PATH/bin/python -m pip install debugpy
    else
        echo "Could not find pip or python in conda environment"
    fi
else
    echo "Conda environment not found at expected location"
    echo "Trying alternative installation methods..."
    
    # Try system-wide installation as fallback
    pip install debugpy --break-system-packages || true
    pip3 install debugpy --break-system-packages || true
fi

echo "Debug installation complete"
