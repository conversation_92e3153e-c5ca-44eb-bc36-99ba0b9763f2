#!/bin/bash
# Script to install debugpy in all conda environments used by Triton

echo "Installing debugpy for debugging..."

# Function to install debugpy in a conda environment
install_in_env() {
    local env_path="$1"
    echo "Installing debugpy in $env_path"

    if [ -f "$env_path/bin/pip" ]; then
        "$env_path/bin/pip" install debugpy 2>/dev/null && echo "  ✓ Success" || echo "  ✗ Failed"
    elif [ -f "$env_path/bin/python" ]; then
        "$env_path/bin/python" -m pip install debugpy 2>/dev/null && echo "  ✓ Success" || echo "  ✗ Failed"
    else
        echo "  ✗ No pip or python found"
    fi
}

# Wait a bit for conda environments to be extracted
sleep 3

# Install in all conda environments
for env_dir in /tmp/python_env_*/0; do
    if [ -d "$env_dir" ]; then
        install_in_env "$env_dir"
    fi
done

# Also try system-wide installation as fallback
echo "Installing debugpy system-wide as fallback..."
pip install debugpy --break-system-packages 2>/dev/null && echo "  ✓ System pip success" || echo "  ✗ System pip failed"
pip3 install debugpy --break-system-packages 2>/dev/null && echo "  ✓ System pip3 success" || echo "  ✗ System pip3 failed"

echo "Debug installation complete"
