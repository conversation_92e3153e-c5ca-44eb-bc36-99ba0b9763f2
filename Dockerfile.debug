FROM registry.rcluster.io/ml-eng/riot-triton-cpu:24.12.10

# Copy debug installation script
COPY install_debug.sh /usr/local/bin/install_debug.sh
RUN chmod +x /usr/local/bin/install_debug.sh

# Install debugpy in system Python as fallback
RUN pip install debugpy --break-system-packages && \
    pip3 install debugpy --break-system-packages

# Expose debug port
EXPOSE 5678

# Create a wrapper script that installs debugpy and starts triton
RUN echo '#!/bin/bash\n\
if [ "$ENABLE_DEBUG" = "true" ]; then\n\
    echo "Debug mode enabled - installing debugpy..."\n\
    /usr/local/bin/install_debug.sh &\n\
fi\n\
exec "$@"' > /usr/local/bin/debug_wrapper.sh && \
chmod +x /usr/local/bin/debug_wrapper.sh

ENTRYPOINT ["/usr/local/bin/debug_wrapper.sh"]
