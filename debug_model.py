#!/usr/bin/env python3
"""
Debug script for testing the model functionality with mock Triton environment.
This simulates the Triton model execution without requiring the full server.
"""

import sys
import os
import json
import numpy as np
from pathlib import Path

# Add the model directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "model_repo" / "models" / "zero_tolerance_bls" / "1"))

# Import the modules
import matcher

class MockTritonRequest:
    """Mock Triton request for debugging"""
    def __init__(self, text_data):
        self.text_data = text_data
        self._cancelled = False
    
    def is_cancelled(self):
        return self._cancelled

class MockTritonTensor:
    """Mock Triton tensor for debugging"""
    def __init__(self, data):
        self.data = data
    
    def as_numpy(self):
        # Convert text data to numpy array format that <PERSON><PERSON> would use
        if isinstance(self.data, list):
            # Create numpy array with shape (batch_size, 1)
            np_data = np.array([[text.encode('utf-8')] for text in self.data], dtype=object)
            return np_data
        else:
            return np.array([[self.data.encode('utf-8')]], dtype=object)

class MockPbUtils:
    """Mock triton_python_backend_utils for debugging"""
    
    @staticmethod
    def get_input_tensor_by_name(request, name):
        if name == "TEXT":
            return MockTritonTensor(request.text_data)
        return None
    
    @staticmethod
    def triton_string_to_numpy(dtype_str):
        return object  # Simplified for debugging
    
    class Tensor:
        def __init__(self, name, data):
            self.name = name
            self.data = data
    
    class InferenceResponse:
        def __init__(self, tensors):
            self.tensors = tensors

class DebugTritonPythonModel:
    """Debug version of the Triton model"""
    
    def __init__(self, zt_model_location=None):
        # Mock the initialization
        if zt_model_location is None:
            # Try to use real data if available
            data_dir = Path("model_repo/engine/data/1.5.0")
            if data_dir.exists():
                zt_model_location = str(data_dir)
            else:
                # Fall back to test data
                test_data_dir = Path("test/unit/data")
                if test_data_dir.exists():
                    zt_model_location = str(test_data_dir)
                else:
                    raise FileNotFoundError("No ZT data found for debugging")
        
        print(f"Initializing model with ZT data from: {zt_model_location}")
        self.zt_lists = matcher.init_zt_lists(zt_model_location)
        self.str_dtype = object  # Simplified for debugging
        
        print(f"Loaded {len(self.zt_lists)} ZT lists: {list(self.zt_lists.keys())}")
    
    def execute(self, requests):
        """Execute inference requests (debug version)"""
        responses = []
        
        for request in requests:
            if request.is_cancelled():
                print("Request was cancelled")
                continue
            
            # Get input text
            input_text = MockPbUtils.get_input_tensor_by_name(request, "TEXT").as_numpy()
            tok_batch = []
            
            print(f"Processing batch of size: {input_text.shape[0]}")
            
            for i in range(input_text.shape[0]):
                decoded_object = input_text[i, 0].decode()
                tok_batch.append(decoded_object)
                print(f"  Input {i}: {repr(decoded_object)}")
            
            # Perform ZT search
            search_results = matcher.zt_search_batch(tok_batch, self.zt_lists)
            
            print("Search results:")
            for i, result in enumerate(search_results):
                print(f"  Result {i}: {result}")
            
            # Create response
            response = MockPbUtils.InferenceResponse([
                MockPbUtils.Tensor("zt_match", np.array(search_results, dtype=self.str_dtype))
            ])
            responses.append(response)
        
        return responses

def debug_single_inference():
    """Debug single text inference"""
    print("=== Single Inference Debug ===")
    
    model = DebugTritonPythonModel()
    
    # Test with single input
    test_text = "I like the color blue, you degenerate"
    request = MockTritonRequest([test_text])
    
    print(f"Testing with: {repr(test_text)}")
    responses = model.execute([request])
    
    print("Response received")
    print()

def debug_batch_inference():
    """Debug batch inference"""
    print("=== Batch Inference Debug ===")
    
    model = DebugTritonPythonModel()
    
    # Test with batch input
    test_batch = [
        "Hello, this is clean text",
        "I like the color blue, you degenerate", 
        "This might have some bad words"
    ]
    
    request = MockTritonRequest(test_batch)
    
    print(f"Testing batch of {len(test_batch)} items")
    responses = model.execute([request])
    
    print("Batch response received")
    print()

def debug_with_custom_text():
    """Debug with custom text input"""
    print("=== Custom Text Debug ===")
    
    model = DebugTritonPythonModel()
    
    # Get custom input from user or set default
    custom_texts = [
        "Enter your test text here",
        "You can add multiple lines",
        "Each will be processed separately"
    ]
    
    print("Testing with custom texts:")
    for text in custom_texts:
        print(f"  {repr(text)}")
    
    request = MockTritonRequest(custom_texts)
    responses = model.execute([request])
    
    print("Custom text processing complete")
    print()

if __name__ == "__main__":
    print("Disruptive Text Model Debug Script")
    print("=" * 50)
    
    try:
        # Set breakpoints in any of these functions to debug specific functionality
        debug_single_inference()
        debug_batch_inference()
        debug_with_custom_text()
        
        print("All debug tests completed successfully!")
        
    except Exception as e:
        print(f"Debug failed with error: {e}")
        import traceback
        traceback.print_exc()
